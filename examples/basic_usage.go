package main

import (
	"fmt"

	"github.com/your-username/go-template/pkg/utils"
	"github.com/your-username/go-template/internal/service"
)

// Example demonstrating basic usage of the go-template components
func main() {
	fmt.Println("=== Go Template Basic Usage Example ===")
	
	// Example 1: Using utility functions
	fmt.Println("\n1. Using utility functions:")
	message := utils.FormatMessage("Welcome", "Go Developer")
	fmt.Println(message)
	
	isEmpty := utils.IsEmpty("")
	fmt.Printf("Is empty string empty? %t\n", isEmpty)
	
	fruits := []string{"apple", "banana", "cherry"}
	contains := utils.Contains(fruits, "banana")
	fmt.Printf("Does fruits contain 'banana'? %t\n", contains)
	
	// Example 2: Using service
	fmt.Println("\n2. Using service:")
	svc := service.New()
	
	// Test case 1
	nums1 := []int{2, 7, 11, 15}
	target1 := 9
	result1 := svc.ProcessData(nums1, target1)
	fmt.Printf("Two sum for %v with target %d: %v\n", nums1, target1, result1)
	
	// Test case 2
	nums2 := []int{3, 2, 4}
	target2 := 6
	result2 := svc.ProcessData(nums2, target2)
	fmt.Printf("Two sum for %v with target %d: %v\n", nums2, target2, result2)
	
	// Test case 3 - no solution
	nums3 := []int{1, 2, 3}
	target3 := 10
	result3 := svc.ProcessData(nums3, target3)
	fmt.Printf("Two sum for %v with target %d: %v\n", nums3, target3, result3)
}
