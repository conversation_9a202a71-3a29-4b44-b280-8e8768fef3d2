.PHONY: build run test clean fmt vet lint help

# Build the application
build:
	go build -o bin/app main.go

# Run the application
run:
	go run main.go

# Run tests
test:
	go test -v ./...

# Run tests with coverage
test-coverage:
	go test -v -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html

# Clean build artifacts
clean:
	rm -rf bin/
	rm -f coverage.out coverage.html

# Format code
fmt:
	go fmt ./...

# Run go vet
vet:
	go vet ./...

# Run golint (requires golint to be installed)
lint:
	golint ./...

# Download dependencies
deps:
	go mod download
	go mod tidy

# Install the application
install:
	go install

# Show help
help:
	@echo "Available commands:"
	@echo "  build         Build the application"
	@echo "  run           Run the application"
	@echo "  test          Run tests"
	@echo "  test-coverage Run tests with coverage"
	@echo "  clean         Clean build artifacts"
	@echo "  fmt           Format code"
	@echo "  vet           Run go vet"
	@echo "  lint          Run golint"
	@echo "  deps          Download and tidy dependencies"
	@echo "  install       Install the application"
	@echo "  help          Show this help message"
