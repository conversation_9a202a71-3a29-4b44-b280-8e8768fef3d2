# Go Template

A well-structured Go project template with best practices and common patterns.

## Project Structure

```
.
├── cmd/                    # Main applications for this project
├── internal/              # Private application and library code
│   ├── app/               # Application logic
│   └── service/           # Business logic services
├── pkg/                   # Library code that's ok to use by external applications
│   └── utils/             # Utility functions
├── api/                   # API definition files (OpenAPI/Swagger specs, JSON schema files, protocol definition files)
├── configs/               # Configuration file templates or default configs
├── scripts/               # Scripts to perform various build, install, analysis, etc operations
├── test/                  # Additional external test apps and test data
├── docs/                  # Design and user documents
├── examples/              # Examples for your applications and/or public libraries
├── web/                   # Web application specific components
├── build/                 # Packaging and Continuous Integration
├── deployments/           # IaaS, PaaS, system and container orchestration deployment configurations and templates
├── tools/                 # Supporting tools for this project
├── vendor/                # Application dependencies (managed by go mod)
├── go.mod                 # Go module file
├── go.sum                 # Go module checksums
├── Makefile               # Build automation
├── .gitignore             # Git ignore file
└── README.md              # This file
```

## Getting Started

### Prerequisites

- Go 1.21 or later
- Make (optional, for using Makefile commands)

### Installation

1. Clone the repository:
```bash
git clone https://github.com/your-username/go-template.git
cd go-template
```

2. Download dependencies:
```bash
go mod download
```

### Running the Application

```bash
# Using go run
go run main.go

# Or using make
make run

# Or build and run
make build
./bin/app
```

### Testing

```bash
# Run all tests
go test ./...

# Or using make
make test

# Run tests with coverage
make test-coverage
```

### Development

```bash
# Format code
make fmt

# Run linter
make vet

# Clean build artifacts
make clean
```

## Available Make Commands

- `make build` - Build the application
- `make run` - Run the application
- `make test` - Run tests
- `make test-coverage` - Run tests with coverage
- `make clean` - Clean build artifacts
- `make fmt` - Format code
- `make vet` - Run go vet
- `make lint` - Run golint
- `make deps` - Download and tidy dependencies
- `make install` - Install the application
- `make help` - Show help message

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
