package main

import (
	"flag"
	"fmt"
	"os"

	"github.com/your-username/go-template/internal/service"
)

func main() {
	var (
		nums   = flag.String("nums", "2,7,11,15", "Comma-separated list of numbers")
		target = flag.Int("target", 9, "Target sum")
	)
	flag.Parse()

	if *nums == "" {
		fmt.Fprintf(os.Stderr, "Usage: %s -nums=2,7,11,15 -target=9\n", os.Args[0])
		os.Exit(1)
	}

	// Parse numbers (simplified for example)
	numbers := []int{2, 7, 11, 15} // In real implementation, parse from *nums

	service := service.New()
	result := service.ProcessData(numbers, *target)

	if result == nil {
		fmt.Println("No solution found")
	} else {
		fmt.Printf("Indices: %v\n", result)
	}
}
