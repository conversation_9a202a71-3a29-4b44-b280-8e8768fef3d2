package app

import (
	"fmt"

	"github.com/your-username/go-template/internal/service"
	"github.com/your-username/go-template/pkg/utils"
)

// App represents the main application
type App struct {
	service *service.Service
}

// New creates a new instance of the application
func New() *App {
	return &App{
		service: service.New(),
	}
}

// Run starts the application
func (a *App) Run() error {
	fmt.Println("Application is running...")
	
	// Example usage of service and utils
	result := a.service.ProcessData([]int{2, 7, 11, 15}, 9)
	fmt.Printf("Two sum result: %v\n", result)
	
	// Example usage of utility function
	message := utils.FormatMessage("Hello", "Go Template")
	fmt.Println(message)
	
	return nil
}
