package service

import (
	"reflect"
	"testing"
)

func TestService_ProcessData(t *testing.T) {
	s := New()
	
	tests := []struct {
		name     string
		nums     []int
		target   int
		expected []int
	}{
		{
			name:     "basic case",
			nums:     []int{2, 7, 11, 15},
			target:   9,
			expected: []int{0, 1},
		},
		{
			name:     "no solution",
			nums:     []int{1, 2, 3},
			target:   10,
			expected: nil,
		},
		{
			name:     "negative numbers",
			nums:     []int{-1, -2, -3, -4, -5},
			target:   -8,
			expected: []int{2, 4},
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := s.ProcessData(tt.nums, tt.target)
			if !reflect.DeepEqual(result, tt.expected) {
				t.<PERSON>rrorf("ProcessData() = %v, want %v", result, tt.expected)
			}
		})
	}
}
