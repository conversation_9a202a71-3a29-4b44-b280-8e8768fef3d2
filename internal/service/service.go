package service

// Service handles business logic
type Service struct {
	// Add service dependencies here
}

// New creates a new service instance
func New() *Service {
	return &Service{}
}

// ProcessData processes the input data and returns two sum result
func (s *Service) ProcessData(nums []int, target int) []int {
	return twoSums(nums, target)
}

// twoSums finds two numbers in the array that add up to the target
func twoSums(nums []int, target int) []int {
	numMap := make(map[int]int)
	
	for i, num := range nums {
		complement := target - num
		if j, exists := numMap[complement]; exists {
			return []int{j, i}
		}
		numMap[num] = i
	}
	
	return nil
}
