package utils

import "fmt"

// FormatMessage formats a message with the given parameters
func FormatMessage(greeting, name string) string {
	return fmt.Sprintf("%s, %s!", greeting, name)
}

// IsEmpty checks if a string is empty
func IsEmpty(s string) bool {
	return len(s) == 0
}

// Contains checks if a slice contains a specific element
func Contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
