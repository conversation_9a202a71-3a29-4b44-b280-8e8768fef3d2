package utils

import "testing"

func TestFormatMessage(t *testing.T) {
	tests := []struct {
		name     string
		greeting string
		nameArg  string
		expected string
	}{
		{
			name:     "basic greeting",
			greeting: "Hello",
			nameArg:  "World",
			expected: "Hello, World!",
		},
		{
			name:     "empty greeting",
			greeting: "",
			nameArg:  "Go",
			expected: ", Go!",
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := FormatMessage(tt.greeting, tt.nameArg)
			if result != tt.expected {
				t.<PERSON><PERSON><PERSON>("FormatMessage() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestIsEmpty(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected bool
	}{
		{"empty string", "", true},
		{"non-empty string", "hello", false},
		{"whitespace", " ", false},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsEmpty(tt.input)
			if result != tt.expected {
				t.<PERSON><PERSON><PERSON>("IsEmpty() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestContains(t *testing.T) {
	slice := []string{"apple", "banana", "cherry"}
	
	tests := []struct {
		name     string
		item     string
		expected bool
	}{
		{"item exists", "banana", true},
		{"item does not exist", "grape", false},
		{"empty item", "", false},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := Contains(slice, tt.item)
			if result != tt.expected {
				t.Errorf("Contains() = %v, want %v", result, tt.expected)
			}
		})
	}
}
