#!/bin/bash

# Build script for go-template

set -e

echo "Building go-template..."

# Create bin directory if it doesn't exist
mkdir -p bin

# Build the main application
echo "Building main application..."
go build -o bin/app main.go

# Build CLI tool
echo "Building CLI tool..."
go build -o bin/cli cmd/cli/main.go

echo "Build completed successfully!"
echo "Binaries are available in the bin/ directory:"
ls -la bin/
